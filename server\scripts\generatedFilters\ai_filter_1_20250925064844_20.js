/**
 * AI自动生成的筛选脚本 (勿直接在生产执行前未审核情况下使用)
 * 生成时间: 2025-09-25T06:48:44.650Z
 * 逻辑索引: 1
 * 逻辑原始描述: 计算近三年净利润增长率的平均值，并筛选出平均值超过20%的股票
 *
 * 使用说明:
 *  1. 可通过 require 动态载入后在沙箱中执行(当前执行服务支持字符串形式)。
 *  2. 如需持久化版本控制，请将本文件纳入 git 并代码审查。
 *  3. 修改本文件后请注意不要引入非白名单模块。
 */

const executeLogic = async () => {
  try {
    const currentYear = 2024;
    const startYear = currentYear - 2;
    
    const result = await FinancialData.aggregate([
      {
        $match: {
          reportType: "按年度",
          fiscalYear: { $gte: startYear, $lte: currentYear }
        }
      },
      {
        $group: {
          _id: "$stockCode",
          growthRates: {
            $push: "$data.keyMetrics.netProfitGrowthRate"
          },
          years: {
            $push: "$fiscalYear"
          }
        }
      },
      {
        $match: {
          "growthRates": { $size: 3 }
        }
      },
      {
        $addFields: {
          avgGrowthRate: {
            $avg: "$growthRates"
          }
        }
      },
      {
        $match: {
          "avgGrowthRate": { $gt: 20 }
        }
      },
      {
        $project: {
          stockCode: "$_id",
          avgGrowthRate: 1,
          growthRates: 1,
          years: 1,
          _id: 0
        }
      },
      {
        $sort: {
          avgGrowthRate: -1
        }
      }
    ]);
    
    return result;
  } catch (error) {
    throw new Error(`查询失败: ${error.message}`);
  }
};
