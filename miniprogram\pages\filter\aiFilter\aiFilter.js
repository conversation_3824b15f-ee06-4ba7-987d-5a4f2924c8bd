// 智能筛选页面
const filterApi = require('../../../api/filter');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户输入的筛选需求
    userInput: '',

    // 加载状态
    isLoading: false,

    // 结果展示
    showResult: false,
    result: null,

    // 错误状态
    showError: false,
    errorMessage: '',

    // 渐进式生成相关
    isProgressiveMode: true, // 是否使用渐进式模式
    currentTaskId: null,     // 当前任务ID
    pollingTimer: null,      // 轮询定时器
    taskStatus: null,        // 任务状态
    logicAnalysis: null,     // 逻辑分析结果
    subLogics: [],           // 子逻辑列表
    hasShownAnalyzedToast: false, // 是否已显示分析完成提示

    // 示例数据
    examples: [
      '找出连续三年净资产收益率大于15%,连续5年净利润增长率大于5%,且当前市盈率小于20,股息率大于3%的股票',
      '找出近三年净利润增长率平均超过20%，且ROE呈上升趋势的股票'
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('智能筛选页面加载');
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('智能筛选页面渲染完成');
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('智能筛选页面显示');
  },

  /**
   * 输入框内容变化
   */
  onInputChange(e) {
    this.setData({
      userInput: e.detail.value,
      showError: false // 清除错误状态
    });
  },

  /**
   * 使用示例
   */
  useExample(e) {
    const example = e.currentTarget.dataset.example;
    this.setData({
      userInput: example,
      showError: false,
      showResult: false
    });
  },

  /**
   * 生成筛选脚本（渐进式）
   */
  async generateScript() {
    const { userInput, isLoading, isProgressiveMode } = this.data;

    // 防止重复点击
    if (isLoading) {
      console.log('正在生成中，忽略重复点击');
      return;
    }

    if (!userInput.trim()) {
      wx.showToast({
        title: '请输入筛选需求',
        icon: 'none'
      });
      return;
    }

    // 检查登录状态
    const app = getApp();
    const isLoggedIn = await app.checkLoginStatus({
      updateUserInfo: false
    });

    if (!isLoggedIn) {
      wx.showModal({
        title: '登录提示',
        content: '使用AI智能筛选功能需要先登录，请选择操作',
        cancelText: '稍后登录',
        confirmText: '立即登录',
        success: (res) => {
          if (res.confirm) {
            // 跳转到登录页面
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
          // 如果选择稍后登录，什么都不做
        }
      });
      return;
    }

    // 根据模式选择不同的生成方式
    if (isProgressiveMode) {
      await this.startProgressiveGeneration();
    } else {
      await this.startTraditionalGeneration();
    }
  },

  /**
   * 启动渐进式生成
   */
  async startProgressiveGeneration() {
    const { userInput } = this.data;

    this.setData({
      isLoading: true,
      showError: false,
      showResult: false,
      taskStatus: 'pending',
      logicAnalysis: null,
      subLogics: [],
      currentTaskId: null,
      hasShownAnalyzedToast: false
    });

    try {
      console.log('启动渐进式生成，用户输入：', userInput);

      // 启动任务
      const response = await filterApi.startProgressiveGeneration(userInput);

      if (response.success) {
        const taskId = response.data.taskId;
        console.log('渐进式生成任务已启动，taskId:', taskId);

        this.setData({
          currentTaskId: taskId,
          taskStatus: response.data.status
        });

        // 开始轮询任务状态
        this.startPolling();

        // 不显示toast，让用户通过页面状态了解进度
      } else {
        throw new Error(response.message || '启动任务失败');
      }
    } catch (error) {
      console.error('启动渐进式生成失败：', error);

      this.setData({
        showError: true,
        errorMessage: error.message || '网络错误，请重试',
        isLoading: false,
        taskStatus: 'failed'
      });

      wx.showToast({
        title: '启动失败',
        icon: 'none'
      });
    }
  },

  /**
   * 传统生成方式（保留作为备用）
   */
  async startTraditionalGeneration() {
    const { userInput } = this.data;

    this.setData({
      isLoading: true,
      showError: false,
      showResult: false
    });

    try {
      console.log('开始传统生成AI筛选脚本，用户输入：', userInput);

      const response = await filterApi.generateAiFilterScript(userInput);

      console.log('AI筛选脚本生成成功：', response);

      if (response.success) {
        this.setData({
          result: response.data,
          showResult: true,
          isLoading: false
        });

        wx.showToast({
          title: '生成成功',
          icon: 'success'
        });
      } else {
        throw new Error(response.message || '生成失败');
      }
    } catch (error) {
      console.error('生成AI筛选脚本失败：', error);

      this.setData({
        showError: true,
        errorMessage: error.message || '网络错误，请重试',
        isLoading: false
      });

      wx.showToast({
        title: '生成失败',
        icon: 'none'
      });
    }
  },

  /**
   * 开始轮询任务状态
   */
  startPolling() {
    // 清除之前的定时器
    if (this.data.pollingTimer) {
      clearInterval(this.data.pollingTimer);
    }

    // 立即查询一次
    this.pollTaskStatus();

    // 设置定时器，每2秒查询一次
    const timer = setInterval(() => {
      this.pollTaskStatus();
    }, 2000);

    this.setData({
      pollingTimer: timer
    });
  },

  /**
   * 停止轮询
   */
  stopPolling() {
    if (this.data.pollingTimer) {
      clearInterval(this.data.pollingTimer);
      this.setData({
        pollingTimer: null
      });
    }
  },

  /**
   * 轮询任务状态
   */
  async pollTaskStatus() {
    const { currentTaskId } = this.data;

    if (!currentTaskId) {
      console.warn('没有当前任务ID，停止轮询');
      this.stopPolling();
      return;
    }

    try {
      const response = await filterApi.getProgressiveGenerationStatus(currentTaskId);

      if (response.success) {
        const taskData = response.data;
        console.log('任务状态更新:', taskData);

        this.setData({
          taskStatus: taskData.status,
          logicAnalysis: taskData.logicAnalysis,
          subLogics: taskData.subLogics || []
        });

        // 根据任务状态更新UI
        this.updateUIByTaskStatus(taskData);

        // 如果任务完成或失败，停止轮询
        if (taskData.status === 'completed' || taskData.status === 'failed') {
          this.stopPolling();
          this.setData({
            isLoading: false
          });

          if (taskData.status === 'completed') {
            this.setData({
              showResult: true
            });
            // 简化提示，不显示toast，让用户通过页面状态了解完成情况
          } else if (taskData.status === 'failed') {
            this.setData({
              showError: true,
              errorMessage: taskData.error || '生成失败'
            });
            // 失败时仍然显示提示
            wx.showToast({
              title: '生成失败',
              icon: 'none'
            });
          }
        }
      } else {
        throw new Error(response.message || '查询状态失败');
      }
    } catch (error) {
      console.error('轮询任务状态失败：', error);

      // 网络错误不停止轮询，继续尝试
      if (error.message && error.message.includes('网络')) {
        console.log('网络错误，继续轮询...');
        return;
      }

      // 其他错误停止轮询
      this.stopPolling();
      this.setData({
        showError: true,
        errorMessage: error.message || '查询状态失败',
        isLoading: false
      });
    }
  },

  /**
   * 根据任务状态更新UI
   */
  updateUIByTaskStatus(taskData) {
    const { status } = taskData;

    // 只在关键状态变化时显示提示，避免频繁弹窗
    switch (status) {
      case 'analyzed':
        // 逻辑分析完成，显示拆分结果（只显示一次）
        if (!this.data.hasShownAnalyzedToast) {
          this.setData({ hasShownAnalyzedToast: true });
        }
        break;
      case 'completed':
        // 在轮询中已经处理了完成状态的提示
        break;
      case 'failed':
        // 在轮询中已经处理了失败状态的提示
        break;
      default:
        break;
    }
  },

  /**
   * 执行脚本
   */
  async executeScript(e) {
    const fileName = e.currentTarget.dataset.filename;
    
    if (!fileName) {
      wx.showToast({
        title: '脚本文件名错误',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '执行中...'
    });

    try {
      console.log('开始执行脚本：', fileName);
      
      // 这里先使用沙箱执行，如果需要可以改为 executeScriptRequire
      const response = await filterApi.executeScriptSandbox(fileName);
      
      console.log('脚本执行成功：', response);
      
      wx.hideLoading();
      
      if (response.success) {
        wx.showToast({
          title: '执行成功',
          icon: 'success'
        });
        
        // TODO: 这里可以跳转到结果页面或者展示执行结果
        // 暂时先显示成功提示
        console.log('脚本执行结果：', response.data);
        
      } else {
        throw new Error(response.message || '执行失败');
      }
    } catch (error) {
      console.error('执行脚本失败：', error);
      
      wx.hideLoading();
      wx.showToast({
        title: error.message || '执行失败',
        icon: 'none'
      });
    }
  },

  /**
   * 重新生成
   */
  regenerateScript() {
    this.setData({
      showResult: false,
      showError: false,
      result: null
    });
    
    // 重新生成
    this.generateScript();
  },

  /**
   * 重试生成
   */
  retryGenerate() {
    this.generateScript();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 停止轮询
    this.stopPolling();

    // 下拉刷新时重置页面状态
    this.setData({
      userInput: '',
      showResult: false,
      showError: false,
      result: null,
      isLoading: false,
      currentTaskId: null,
      taskStatus: null,
      logicAnalysis: null,
      subLogics: []
    });

    wx.stopPullDownRefresh();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 页面隐藏时停止轮询
    this.stopPolling();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 页面卸载时停止轮询
    this.stopPolling();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '言策AI智能筛选 - 用自然语言筛选股票',
      path: '/pages/filter/aiFilter/aiFilter'
    };
  }
});
