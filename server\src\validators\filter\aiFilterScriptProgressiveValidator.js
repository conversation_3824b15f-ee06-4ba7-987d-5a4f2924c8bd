const Joi = require('joi');

// 验证启动渐进式生成任务的 schema
const startProgressiveGenerationSchema = Joi.object({
  userInput: Joi.string()
    .trim()
    .min(5)
    .max(1000)
    .required()
    .messages({
      'string.empty': '用户输入不能为空',
      'string.min': '用户输入长度必须至少5个字符',
      'string.max': '用户输入长度不能超过1000个字符',
      'any.required': '用户输入不能为空'
    })
}).required();

// 验证查询任务状态的 schema
const getTaskStatusSchema = Joi.object({
  taskId: Joi.string()
    .trim()
    .min(10)
    .max(100)
    .required()
    .messages({
      'string.empty': '任务ID不能为空',
      'string.min': '任务ID长度不正确',
      'string.max': '任务ID长度不正确',
      'any.required': '任务ID不能为空'
    })
}).required();

/**
 * 验证启动渐进式生成任务的请求
 */
const validateStartProgressiveGenerationRequest = (req, res, next) => {
  const { error, value } = startProgressiveGenerationSchema.validate(req.body, {
    abortEarly: false,
    allowUnknown: false
  });

  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value
    }));

    console.log('[渐进式生成验证器] 启动任务请求验证失败:', errors);
    return res.status(400).json({
      success: false,
      message: '请求参数验证失败',
      errors
    });
  }

  req.validatedBody = value;
  next();
};

/**
 * 验证查询任务状态的请求
 */
const validateGetTaskStatusRequest = (req, res, next) => {
  const { error, value } = getTaskStatusSchema.validate(req.params, {
    abortEarly: false,
    allowUnknown: false
  });

  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value
    }));

    console.log('[渐进式生成验证器] 查询状态请求验证失败:', errors);
    return res.status(400).json({
      success: false,
      message: '请求参数验证失败',
      errors
    });
  }

  req.validatedParams = value;
  next();
};

module.exports = {
  validateStartProgressiveGenerationRequest,
  validateGetTaskStatusRequest
};
