const { body, param, validationResult } = require('express-validator');

/**
 * 验证启动渐进式生成任务的请求
 */
const validateStartProgressiveGenerationRequest = [
  body('userInput')
    .notEmpty()
    .withMessage('用户输入不能为空')
    .isString()
    .withMessage('用户输入必须是字符串')
    .isLength({ min: 5, max: 1000 })
    .withMessage('用户输入长度必须在5-1000字符之间')
    .trim(),

  // 验证结果处理中间件
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('[渐进式生成验证器] 启动任务请求验证失败:', errors.array());
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: errors.array().map(error => ({
          field: error.path,
          message: error.msg,
          value: error.value
        }))
      });
    }
    next();
  }
];

/**
 * 验证查询任务状态的请求
 */
const validateGetTaskStatusRequest = [
  param('taskId')
    .notEmpty()
    .withMessage('任务ID不能为空')
    .isString()
    .withMessage('任务ID必须是字符串')
    .isLength({ min: 10, max: 100 })
    .withMessage('任务ID长度不正确')
    .trim(),

  // 验证结果处理中间件
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('[渐进式生成验证器] 查询状态请求验证失败:', errors.array());
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: errors.array().map(error => ({
          field: error.path,
          message: error.msg,
          value: error.value
        }))
      });
    }
    next();
  }
];

module.exports = {
  validateStartProgressiveGenerationRequest,
  validateGetTaskStatusRequest
};
