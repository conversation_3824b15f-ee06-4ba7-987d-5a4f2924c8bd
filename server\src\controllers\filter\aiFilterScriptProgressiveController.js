const { 
  startProgressiveGeneration, 
  getTaskStatus 
} = require('../../services/filter/aiFilterScriptProgressiveService');
const { success } = require('../../utils/response');

/**
 * 启动渐进式AI生成筛选脚本任务
 * @route POST /api/filter/ai-generate-script-progressive
 * @access Private (需要鉴权)
 * @body {string} userInput - 用户自然语言描述
 * @returns {Object} 任务启动结果
 */
const startProgressiveGenerationController = async (req, res, next) => {
  try {
    const { userInput } = req.body;
    console.log('[渐进式生成控制器] 收到启动请求 userInput:', userInput);

    const result = await startProgressiveGeneration(userInput);

    return success(res, 200, '渐进式生成任务已启动', result);
  } catch (err) {
    console.error('[渐进式生成控制器] 启动任务失败:', err);
    next(err);
  }
};

/**
 * 查询渐进式生成任务状态
 * @route GET /api/filter/ai-generate-script-progressive/:taskId
 * @access Private (需要鉴权)
 * @param {string} taskId - 任务ID
 * @returns {Object} 任务状态信息
 */
const getTaskStatusController = async (req, res, next) => {
  try {
    const { taskId } = req.params;
    console.log('[渐进式生成控制器] 收到状态查询请求 taskId:', taskId);

    const result = await getTaskStatus(taskId);

    return success(res, 200, '查询任务状态成功', result.data);
  } catch (err) {
    console.error('[渐进式生成控制器] 查询任务状态失败:', err);
    next(err);
  }
};

module.exports = { 
  startProgressiveGenerationController,
  getTaskStatusController
};
