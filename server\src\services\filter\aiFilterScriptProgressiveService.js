const { generateTextWithDeepSeek } = require('../../utils/deepseekApi');
const DailyStockData = require('../../models/DailyStockData');
const FinancialData = require('../../models/FinancialData');
const StockBasicInfo = require('../../models/StockBasicInfo');
// 引入数据模型摘要信息
const { 
  dailyStockDataModelInfo,
  financialDataModelInfo,
  stockBasicInfoModelInfo
} = require('../../config/databaseAbstract');
// 新增: 用于保存生成脚本到本地文件
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// 生成脚本保存的基础目录
const GENERATED_BASE_DIR = path.join(__dirname, '../../../scripts/generatedFilters');

// 任务状态枚举
const TASK_STATUS = {
  PENDING: 'pending',           // 等待开始
  ANALYZING: 'analyzing',       // 正在分析逻辑
  ANALYZED: 'analyzed',         // 逻辑分析完成
  GENERATING: 'generating',     // 正在生成脚本
  COMPLETED: 'completed',       // 全部完成
  FAILED: 'failed'             // 失败
};

// 子逻辑状态枚举
const SUB_LOGIC_STATUS = {
  PENDING: 'pending',           // 等待生成
  GENERATING: 'generating',     // 正在生成
  COMPLETED: 'completed',       // 生成完成
  FAILED: 'failed'             // 生成失败
};

// 内存中存储任务状态（生产环境建议使用Redis）
const taskStore = new Map();

/**
 * 启动渐进式AI筛选脚本生成任务
 * @param {string} userInput 用户输入的筛选需求描述
 * @returns {Promise<Object>} 任务信息
 */
const startProgressiveGeneration = async (userInput) => {
  try {
    // 生成任务ID
    const taskId = uuidv4();
    
    // 初始化任务状态
    const task = {
      taskId,
      userInput,
      status: TASK_STATUS.PENDING,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      logicAnalysis: null,
      subLogics: [],
      error: null
    };
    
    // 存储任务
    taskStore.set(taskId, task);
    
    console.log(`[渐进式生成] 启动任务 ${taskId}，用户输入: ${userInput}`);
    
    // 异步执行生成流程
    executeGenerationFlow(taskId).catch(error => {
      console.error(`[渐进式生成] 任务 ${taskId} 执行失败:`, error);
      updateTaskStatus(taskId, TASK_STATUS.FAILED, { error: error.message });
    });
    
    return {
      success: true,
      taskId,
      status: task.status,
      message: '任务已启动，正在分析逻辑...'
    };
    
  } catch (error) {
    console.error('[渐进式生成] 启动任务失败:', error);
    throw new Error(`启动渐进式生成任务失败: ${error.message}`);
  }
};

/**
 * 查询任务状态
 * @param {string} taskId 任务ID
 * @returns {Promise<Object>} 任务状态信息
 */
const getTaskStatus = async (taskId) => {
  try {
    const task = taskStore.get(taskId);
    
    if (!task) {
      throw new Error('任务不存在或已过期');
    }
    
    return {
      success: true,
      data: {
        taskId: task.taskId,
        status: task.status,
        userInput: task.userInput,
        createdAt: task.createdAt,
        updatedAt: task.updatedAt,
        logicAnalysis: task.logicAnalysis,
        subLogics: task.subLogics,
        error: task.error
      }
    };
    
  } catch (error) {
    console.error('[渐进式生成] 查询任务状态失败:', error);
    throw new Error(`查询任务状态失败: ${error.message}`);
  }
};

/**
 * 执行生成流程
 * @param {string} taskId 任务ID
 */
const executeGenerationFlow = async (taskId) => {
  try {
    const task = taskStore.get(taskId);
    if (!task) {
      throw new Error('任务不存在');
    }
    
    // 第一步：分析逻辑
    updateTaskStatus(taskId, TASK_STATUS.ANALYZING);
    console.log(`[渐进式生成] 任务 ${taskId} 开始分析逻辑`);
    
    const logicAnalysis = await parseUserLogicToSubLogics(task.userInput);
    
    // 更新任务状态为已分析
    updateTaskStatus(taskId, TASK_STATUS.ANALYZED, { 
      logicAnalysis,
      subLogics: (logicAnalysis.subLogics || []).map((subLogic, index) => ({
        index: index + 1,
        description: subLogic.description,
        usedDatasets: subLogic.usedDatasets,
        status: SUB_LOGIC_STATUS.PENDING,
        fileName: null,
        filePath: null,
        error: null,
        generatedAt: null
      }))
    });
    
    console.log(`[渐进式生成] 任务 ${taskId} 逻辑分析完成，子逻辑数量: ${logicAnalysis.subLogics?.length || 0}`);
    
    // 第二步：生成脚本
    updateTaskStatus(taskId, TASK_STATUS.GENERATING);
    
    const updatedTask = taskStore.get(taskId);
    if (updatedTask.subLogics && updatedTask.subLogics.length > 0) {
      // 逐个生成子逻辑脚本
      for (const subLogic of updatedTask.subLogics) {
        await generateSingleSubLogicScript(taskId, subLogic);
      }
    }
    
    // 第三步：完成
    updateTaskStatus(taskId, TASK_STATUS.COMPLETED);
    console.log(`[渐进式生成] 任务 ${taskId} 全部完成`);
    
  } catch (error) {
    console.error(`[渐进式生成] 任务 ${taskId} 执行失败:`, error);
    updateTaskStatus(taskId, TASK_STATUS.FAILED, { error: error.message });
  }
};

/**
 * 生成单个子逻辑脚本
 * @param {string} taskId 任务ID
 * @param {Object} subLogic 子逻辑信息
 */
const generateSingleSubLogicScript = async (taskId, subLogic) => {
  try {
    // 更新子逻辑状态为生成中
    updateSubLogicStatus(taskId, subLogic.index, SUB_LOGIC_STATUS.GENERATING);
    
    console.log(`[渐进式生成] 任务 ${taskId} 开始生成子逻辑 ${subLogic.index}: ${subLogic.description}`);
    
    // 构建第二个Agent的系统提示词
    const systemPrompt = buildScriptGeneratorSystemPrompt(subLogic.usedDatasets);
    
    // 构建用户提示词
    const userPrompt = buildScriptGeneratorUserPrompt(subLogic);
    
    // 调用DeepSeek API
    const response = await generateTextWithDeepSeek(userPrompt, systemPrompt, {
      temperature: 0.0,
      maxTokens: 3000
    });
    
    // 解析AI返回的脚本代码
    const scriptCode = parseScriptResponse(response);
    
    // 保存到文件系统
    const saveResult = await writeScriptFile({
      logicIndex: subLogic.index,
      logicDescription: subLogic.description,
      scriptCode
    });
    
    // 更新子逻辑状态为完成
    updateSubLogicStatus(taskId, subLogic.index, SUB_LOGIC_STATUS.COMPLETED, {
      fileName: saveResult.fileName,
      filePath: saveResult.filePath,
      fileSaved: saveResult.saved,
      fileSaveError: saveResult.saved ? null : saveResult.error || null,
      generatedAt: new Date().toISOString()
    });
    
    console.log(`[渐进式生成] 任务 ${taskId} 子逻辑 ${subLogic.index} 生成完成`);
    
  } catch (error) {
    console.error(`[渐进式生成] 任务 ${taskId} 子逻辑 ${subLogic.index} 生成失败:`, error);
    updateSubLogicStatus(taskId, subLogic.index, SUB_LOGIC_STATUS.FAILED, {
      error: error.message
    });
  }
};

/**
 * 更新任务状态
 * @param {string} taskId 任务ID
 * @param {string} status 新状态
 * @param {Object} updates 其他更新字段
 */
const updateTaskStatus = (taskId, status, updates = {}) => {
  const task = taskStore.get(taskId);
  if (task) {
    task.status = status;
    task.updatedAt = new Date().toISOString();
    Object.assign(task, updates);
    taskStore.set(taskId, task);
  }
};

/**
 * 更新子逻辑状态
 * @param {string} taskId 任务ID
 * @param {number} subLogicIndex 子逻辑索引
 * @param {string} status 新状态
 * @param {Object} updates 其他更新字段
 */
const updateSubLogicStatus = (taskId, subLogicIndex, status, updates = {}) => {
  const task = taskStore.get(taskId);
  if (task && task.subLogics) {
    const subLogic = task.subLogics.find(sl => sl.index === subLogicIndex);
    if (subLogic) {
      subLogic.status = status;
      Object.assign(subLogic, updates);
      task.updatedAt = new Date().toISOString();
      taskStore.set(taskId, task);
    }
  }
};

// 确保目录存在
const ensureGeneratedDirExists = () => {
  try {
    if (!fs.existsSync(GENERATED_BASE_DIR)) {
      fs.mkdirSync(GENERATED_BASE_DIR, { recursive: true });
      console.log('[渐进式生成] 已创建目录:', GENERATED_BASE_DIR);
    }
  } catch (e) {
    console.warn('[渐进式生成] 创建目录失败:', e.message);
  }
};

// 生成安全的文件名
const buildScriptFileName = (logicIndex, description) => {
  const timeStr = new Date().toISOString().replace(/[-:TZ.]/g, '').slice(0, 14);
  const raw = (description || 'logic').toString().slice(0, 30);
  const ascii = raw
    .replace(/[\u4e00-\u9fa5]/g, '')
    .replace(/[^a-zA-Z0-9]+/g, '_')
    .replace(/_+/g, '_')
    .replace(/^_|_$/g, '')
    .toLowerCase();
  const slug = ascii || 'logic';
  return `ai_filter_${logicIndex}_${timeStr}_${slug}.js`;
};

// 写入脚本文件
const writeScriptFile = async ({ logicIndex, logicDescription, scriptCode }) => {
  ensureGeneratedDirExists();
  const fileName = buildScriptFileName(logicIndex, logicDescription);
  const filePath = path.join(GENERATED_BASE_DIR, fileName);

  const header = `/**
 * AI自动生成的筛选脚本 (勿直接在生产执行前未审核情况下使用)
 * 生成时间: ${new Date().toISOString()}
 * 逻辑索引: ${logicIndex}
 * 逻辑原始描述: ${logicDescription}
 *
 * 使用说明:
 *  1. 可通过 require 动态载入后在沙箱中执行(当前执行服务支持字符串形式)。
 *  2. 如需持久化版本控制，请将本文件纳入 git 并代码审查。
 *  3. 修改本文件后请注意不要引入非白名单模块。
 */

`;

  const content = header + scriptCode + (scriptCode.endsWith('\n') ? '' : '\n');

  try {
    await fs.promises.writeFile(filePath, content, { encoding: 'utf8', flag: 'w' });
    console.log('[渐进式生成] 已保存脚本到文件:', filePath);
    return { saved: true, filePath, fileName };
  } catch (err) {
    console.warn('[渐进式生成] 保存脚本文件失败:', err.message);
    return { saved: false, filePath, fileName, error: err.message };
  }
};

/**
 * 第一个Agent：解析用户逻辑，判断是否需要拆分成子逻辑
 * @param {string} userInput 用户输入
 * @returns {Promise<Object>} 逻辑分析结果
 */
const parseUserLogicToSubLogics = async (userInput) => {
  try {
    // 构建第一个Agent的系统提示词
    const systemPrompt = buildLogicParserSystemPrompt();

    // 构建用户提示词
    const userPrompt = buildLogicParserUserPrompt(userInput);

    // 调用DeepSeek API
    const response = await generateTextWithDeepSeek(userPrompt, systemPrompt, {
      temperature: 0.0,
      maxTokens: 2000
    });

    // 解析AI返回的逻辑分析
    const logicAnalysis = parseSubLogicsResponse(response);

    return logicAnalysis;
  } catch (error) {
    throw new Error(`解析用户逻辑失败: ${error.message}`);
  }
};

/**
 * 构建第一个Agent的系统提示词
 * @returns {string} 系统提示词
 */
const buildLogicParserSystemPrompt = () => {
  // 获取字段映射
  const dailyMapping = typeof DailyStockData.getFieldMapping === 'function' ? DailyStockData.getFieldMapping() : {};
  const financialMapping = typeof FinancialData.getFieldMapping === 'function' ? FinancialData.getFieldMapping() : {};
  const basicMapping = typeof StockBasicInfo.getFieldMapping === 'function' ? StockBasicInfo.getFieldMapping() : {};

  const currentYear = new Date().getFullYear();
  const lastCompleteYear = currentYear - 1;

  const formatMapping = (mappingObj) => {
    return Object.entries(mappingObj)
      .map(([cn, en]) => `- ${cn} => ${en}`)
      .join('\n');
  };

  const dailyMappingStr = formatMapping(dailyMapping);
  const financialMappingStr = formatMapping(financialMapping);
  const basicMappingStr = formatMapping(basicMapping);

  return `你是一个专业的股票筛选逻辑分析专家。你的任务：读取用户的自然语言筛选/计算需求，判断是否需要拆分成多个"子逻辑"以便后续生成可执行脚本。

为提升你对需求复杂度的判断，下面提供当前系统中可用的数据库字段映射（中文 -> 英文字段标识）。

================ 可用数据表字段 ================
【dailyStockData 当日数据】(短周期/行情 / 估值 / 交易特征)
${dailyMappingStr}

【financialData 财务年度/报告期数据】(中长期经营 / 盈利能力 / 资产负债 / 现金流 / 成长性)
${financialMappingStr}

【stockBasicInfo 基本信息】(静态属性 / 行业 / 板块 / 上市时间)
${basicMappingStr}
================================================================================

【年度数据滞后规则（必须遵守并在分析中内化）】
1. FinancialData 年度数据存在披露滞后：当前自然年 ${currentYear} 尚无完整年度数据，最新可用完整年度 = ${lastCompleteYear}。
2. 用户使用"今年 / 本年度 / ${currentYear} 年" 描述年度财务指标时，应自动解释为"上一完整年度 ${lastCompleteYear}"。
3. "最近N年" 序列需从 ${lastCompleteYear} 向前推 N-1 年；例：最近3年 => ${lastCompleteYear}, ${lastCompleteYear - 1}, ${lastCompleteYear - 2}。
4. 严禁引用未来或未完成年度 (${currentYear}) 的年度汇总值；如用户显式提及，需在子逻辑描述中注明已替换为 ${lastCompleteYear}。
5. 若需求需要体现当前年度进展，应提示改用日级或报告期(季度/半年度)数据，而非年度汇总。


**什么情况需要拆分子逻辑：**
1. 需要复杂计算的逻辑（如：计算多年平均值、增长率趋势、形态识别等）
2. 需要多步骤处理的逻辑（如：先筛选A条件，再基于结果计算B指标）
3. 涉及跨时间段的复杂分析（如：连续N年满足某条件的变化趋势）
4. 需要自定义指标计算的逻辑（如：自定义评分、综合排名等）

**什么情况不需要拆分：**
1. 简单的条件筛选（如：市盈率在某个范围、行业筛选、单一指标比较）
2. 基本的数据查询（如：连续三年净利润大于0，这只是简单的多年数据筛选）
3. 直接的字段比较（如：收盘价大于某值、成交量在某范围）

【输出要求】
{
  "needSplit": boolean,//表示是否需要拆分
  "reason": "不需要拆分的原因(中文)",
  "subLogics": [{
    "description": "对整体需求的精炼业务描述",
    "usedDatasets": ["dailyStockData", "financialData"] //表示该该逻辑中的计算和筛选需要用到的数据表
  }]
}

**重要原则：**
1. 优先考虑不拆分，只有真正复杂的逻辑才拆分
2. 子逻辑描述要用自然语言，不要包含具体的字段名和操作符
3. 每个子逻辑应该代表一个独立的业务概念,子逻辑要相互没有依赖关系
4. 拆分后的子逻辑应该比原始需求更容易理解和实现

请严格只输出 JSON。`;
};

/**
 * 构建第一个Agent的用户提示词
 * @param {string} userInput 用户输入
 * @returns {string} 用户提示词
 */
const buildLogicParserUserPrompt = (userInput) => {
  return `请分析以下用户的股票筛选需求，判断是否需要拆分成子逻辑：

用户需求："${userInput}"

请仔细分析：
1. 这个需求是否涉及复杂计算或多步骤处理？
2. 是否可以通过简单的数据库查询直接实现？
3. 是否需要自定义指标计算或复杂的业务逻辑？

如果是简单的筛选条件（如：连续三年净利润大于0、市盈率在某范围、行业筛选等），请不要拆分。
只有真正复杂的逻辑才需要拆分成子逻辑。

只返回JSON格式，不要任何额外说明。`;
};

/**
 * 构建第二个Agent的系统提示词
 * @param {Array} usedDatasets 使用的数据集
 * @returns {string} 系统提示词
 */
const buildScriptGeneratorSystemPrompt = (usedDatasets) => {
  // 直接使用数据库摘要信息
  const dailyFields = dailyStockDataModelInfo;
  const financialFields = financialDataModelInfo;
  const basicInfoFields = stockBasicInfoModelInfo;

  const currentYear = new Date().getFullYear();
  const lastCompleteYear = currentYear - 1;

  // 根据 usedDatasets 过滤需要的表
  const VALID_KEYS = ['dailyStockData','financialData','stockBasicInfo'];
  let ds = Array.isArray(usedDatasets) ? usedDatasets.filter(v => VALID_KEYS.includes(v)) : [];
  if (ds.length === 0) ds = VALID_KEYS; // 回退

  const parts = [];
  if (ds.includes('dailyStockData')) {
    parts.push(`**1. DailyStockData (当日股票数据表)：**\n${dailyFields}`);
  }
  if (ds.includes('financialData')) {
    parts.push(`**2. FinancialData (财务数据表)：**\n${financialFields}`);
  }
  if (ds.includes('stockBasicInfo')) {
    parts.push(`**3. StockBasicInfo (股票基本信息表)：**\n${basicInfoFields}`);
  }
  const fieldBlock = parts.join('\n\n');

  return `你是一个专业的MongoDB查询脚本生成专家。你的任务是根据给定的逻辑描述生成完整的、可执行的JavaScript代码。

**可用的数据表和字段：**
${fieldBlock}

【年度数据滞后约束（必须在代码逻辑中严格遵守）】
1. 财务年度数据存在披露滞后：当前自然年 ${currentYear} 的年度汇总尚未完整，最新可用完整年度 = ${lastCompleteYear}。
2. 用户要求 "今年 / 本年度 / ${currentYear} 年" 的年度指标时，代码中必须改用 ${lastCompleteYear} 年度数据字段/过滤。
3. "最近N年" 序列起点为 ${lastCompleteYear}，向前推 N-1 年；不得生成含 ${currentYear} 年的年度判断。
4. 若需体现当前年份进展，应改用日级(DailyStockData)或报告期数据（若后续提供），禁止伪造未披露年度值。
5. 任何循环或聚合年份逻辑需显式排除 > ${lastCompleteYear} 的年度。

**你需要生成的代码特点：**
1. 完整的可执行JavaScript函数
2. 使用Mongoose进行数据库查询
3. 包含错误处理
4. 返回符合条件的股票列表
5. 代码简洁高效，只包含核心逻辑
6. 根据逻辑描述选择合适的字段和操作符

**代码模板结构：**
\`\`\`javascript
const executeLogic = async () => {
  try {
    // 核心查询逻辑
    const result = await Model.find(conditions);
    return result;
  } catch (error) {
    throw new Error(\`查询失败: \${error.message}\`);
  }
};
\`\`\`

**重要要求：**
1. 根据逻辑描述选择正确的数据表和字段
2. 使用正确的MongoDB查询语法和操作符
3. 对于跨年度的财务数据查询，使用聚合管道或多次查询，并注意年度滞后规则
4. 确保代码可以直接运行
5. 包含基本的错误处理
6. 只返回JavaScript代码，不要任何解释或markdown标记`;
};

/**
 * 构建第二个Agent的用户提示词
 * @param {Object} logicInfo 逻辑信息对象
 * @returns {string} 用户提示词
 */
const buildScriptGeneratorUserPrompt = (logicInfo) => {
  return `请为以下逻辑描述生成完整的可执行JavaScript代码：

逻辑描述：${logicInfo.description}

请仔细分析这个逻辑描述，选择合适的数据表、字段和查询条件，生成一个完整的JavaScript函数。

特别注意：
1. 对于"连续N年"的条件，需要查询多个年度的财务数据
2. 选择最合适的字段名（参考上面的字段列表）
3. 使用正确的MongoDB查询语法
4. 确保查询逻辑符合业务需求

只返回JavaScript代码，不要任何额外说明或markdown标记。`;
};

/**
 * 解析逻辑分析响应
 * @param {string} response AI返回的响应
 * @returns {Object} 解析结果
 */
const parseSubLogicsResponse = (response) => {
  try {
    console.log('[渐进式生成] AI返回的逻辑分析:', response);

    // 去除可能的Markdown代码块标记
    let cleanResponse = response.trim();
    if (cleanResponse.startsWith('```json')) {
      cleanResponse = cleanResponse.replace(/^```json\s*/, '');
    }
    if (cleanResponse.startsWith('```')) {
      cleanResponse = cleanResponse.replace(/^```\s*/, '');
    }
    if (cleanResponse.endsWith('```')) {
      cleanResponse = cleanResponse.replace(/\s*```$/, '');
    }

    // 解析JSON
    const parsed = JSON.parse(cleanResponse);

    // 验证必要字段
    if (typeof parsed.needSplit !== 'boolean') {
      throw new Error('返回结果必须包含needSplit字段');
    }

    return parsed;
  } catch (error) {
    throw new Error(`无法解析AI返回的逻辑分析: ${error.message}。返回内容: ${response}`);
  }
};

/**
 * 解析脚本响应
 * @param {string} response AI返回的响应
 * @returns {string} 脚本代码
 */
const parseScriptResponse = (response) => {
  try {
    // 去除可能的Markdown代码块标记
    let cleanResponse = response.trim();
    if (cleanResponse.startsWith('```javascript')) {
      cleanResponse = cleanResponse.replace(/^```javascript\s*/, '');
    }
    if (cleanResponse.startsWith('```js')) {
      cleanResponse = cleanResponse.replace(/^```js\s*/, '');
    }
    if (cleanResponse.startsWith('```')) {
      cleanResponse = cleanResponse.replace(/^```\s*/, '');
    }
    if (cleanResponse.endsWith('```')) {
      cleanResponse = cleanResponse.replace(/\s*```$/, '');
    }

    // 验证代码是否包含基本的函数结构
    if (!cleanResponse.includes('async') || !cleanResponse.includes('await')) {
      console.warn('[渐进式生成] 生成的代码可能不包含异步函数结构');
    }

    return cleanResponse;
  } catch (error) {
    throw new Error(`无法解析AI返回的脚本代码: ${error.message}。返回内容: ${response}`);
  }
};

module.exports = {
  startProgressiveGeneration,
  getTaskStatus,
  TASK_STATUS,
  SUB_LOGIC_STATUS
};
