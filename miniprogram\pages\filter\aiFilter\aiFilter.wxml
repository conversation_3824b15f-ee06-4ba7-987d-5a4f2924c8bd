<!--智能筛选页面-->
<view class="ai-filter-page">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="title-section">
      <text class="page-title">智能筛选</text>
      <text class="page-subtitle">用自然语言描述您的筛选需求，AI为您生成专业的筛选脚本</text>
    </view>
  </view>

  <!-- 智能输入区域 -->
  <view class="ai-input-section">
    <view class="input-container">
      <view class="input-header">
        <text class="input-title">描述您的筛选需求</text>
        <text class="input-subtitle">例如：找出市盈率低于20倍，净资产收益率大于15%的科技股</text>
      </view>
      
      <view class="input-area">
        <textarea 
          class="ai-input"
          placeholder="请用自然语言描述您的筛选条件..."
          value="{{userInput}}"
          bindinput="onInputChange"
          maxlength="1000"
          auto-height
          show-confirm-bar="{{false}}"
        />
        <view class="input-footer">
          <text class="char-count">{{userInput.length}}/1000</text>
        </view>
      </view>
    </view>

    <!-- 示例区域 -->
    <view class="examples-section" wx:if="{{!userInput.trim()}}">
      <text class="examples-title">💡 试试这些示例：</text>
      <view class="examples-list">
        <view 
          class="example-item" 
          wx:for="{{examples}}" 
          wx:key="index"
          bindtap="useExample" 
          data-example="{{item}}"
        >
          <text class="example-text">{{item}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 生成按钮 -->
  <view class="generate-section">
    <view class="ai-filter-actions">
      <view
        class="btn-parse {{isLoading ? 'loading disabled' : 'active'}}"
        bindtap="{{isLoading ? '' : 'generateScript'}}"
      >
        {{isLoading ? '生成中...' : '生成筛选脚本'}}
      </view>
    </view>
  </view>

  <!-- 渐进式生成状态显示 -->
  <view class="progressive-section" wx:if="{{isLoading && currentTaskId}}">
    <!-- 当前状态 -->
    <view class="current-status">
      <text class="status-text">
        {{taskStatus === 'pending' ? 'AI正在准备中...' :
          taskStatus === 'analyzing' ? 'AI正在梳理逻辑...' :
          taskStatus === 'analyzed' ? '逻辑梳理完成，开始生成脚本' :
          taskStatus === 'generating' ? '正在生成脚本...' :
          taskStatus === 'completed' ? '全部完成' :
          taskStatus === 'failed' ? '生成失败' : 'AI正在工作中...'}}
      </text>
    </view>

    <!-- 逻辑分析结果 -->
    <view class="logic-result" wx:if="{{logicAnalysis}}">
      <view class="section-title">逻辑梳理结果</view>
      <view class="logic-reason">{{logicAnalysis.reason}}</view>
    </view>

    <!-- 子逻辑生成进度 -->
    <view class="scripts-progress" wx:if="{{subLogics && subLogics.length > 0}}">
      <view class="section-title">脚本生成进度 ({{subLogics.length}}个)</view>
      <view class="progress-list">
        <view
          class="progress-item"
          wx:for="{{subLogics}}"
          wx:key="index"
          wx:for-item="subLogic"
        >
          <view class="item-content">
            <view class="item-desc">{{subLogic.index}}. {{subLogic.description}}</view>
            <view class="item-status {{subLogic.status}}">
              {{subLogic.status === 'pending' ? '等待中' :
                subLogic.status === 'generating' ? '生成中' :
                subLogic.status === 'completed' ? '已完成' :
                subLogic.status === 'failed' ? '失败' : '未知'}}
            </view>
          </view>
          <!-- 生成完成后显示执行按钮 -->
          <view class="item-action" wx:if="{{subLogic.status === 'completed' && subLogic.fileName}}">
            <button
              class="execute-btn"
              bindtap="executeScript"
              data-filename="{{subLogic.fileName}}"
            >
              执行脚本
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 结果展示区域（渐进式完成后） -->
  <view class="result-section" wx:if="{{showResult}}">
    <view class="section-title">生成完成</view>

    <!-- 渐进式模式：显示子逻辑结果 -->
    <view class="final-scripts" wx:if="{{isProgressiveMode && subLogics && subLogics.length > 0}}">
      <view class="scripts-count">共生成 {{subLogics.length}} 个脚本</view>
      <view class="scripts-list">
        <view
          class="script-item"
          wx:for="{{subLogics}}"
          wx:key="index"
          wx:for-item="subLogic"
          wx:if="{{subLogic.status === 'completed'}}"
        >
          <view class="script-info">
            <view class="script-desc">{{subLogic.index}}. {{subLogic.description}}</view>
            <view class="script-file">{{subLogic.fileName}}</view>
          </view>
          <button
            class="execute-btn"
            bindtap="executeScript"
            data-filename="{{subLogic.fileName}}"
          >
            执行脚本
          </button>
        </view>
      </view>
    </view>

    <!-- 传统模式：显示脚本列表 -->
    <view class="final-scripts" wx:if="{{!isProgressiveMode && result && result.scripts && result.scripts.length > 0}}">
      <view class="scripts-count">共生成 {{result.scripts.length}} 个脚本</view>
      <view class="scripts-list">
        <view
          class="script-item"
          wx:for="{{result.scripts}}"
          wx:key="fileName"
          wx:for-item="script"
        >
          <view class="script-info">
            <view class="script-desc">{{script.description}}</view>
            <view class="script-file">{{script.fileName}}</view>
          </view>
          <button
            class="execute-btn"
            bindtap="executeScript"
            data-filename="{{script.fileName}}"
          >
            执行脚本
          </button>
        </view>
      </view>
    </view>

    <!-- 重新生成按钮 -->
    <view class="regenerate-action">
      <button class="regenerate-btn" bindtap="regenerateScript">
        重新生成
      </button>
    </view>
  </view>

  <!-- 错误提示 -->
  <view class="error-section" wx:if="{{showError}}">
    <view class="error-card">
      <text class="error-icon">⚠️</text>
      <text class="error-title">生成失败</text>
      <text class="error-message">{{errorMessage}}</text>
      <button class="btn-retry" bindtap="retryGenerate">重试</button>
    </view>
  </view>


</view>
