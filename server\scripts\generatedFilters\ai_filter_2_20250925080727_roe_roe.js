/**
 * AI自动生成的筛选脚本 (勿直接在生产执行前未审核情况下使用)
 * 生成时间: 2025-09-25T08:07:27.657Z
 * 逻辑索引: 2
 * 逻辑原始描述: 分析近三年ROE的变化趋势，筛选出ROE呈持续上升态势的股票
 *
 * 使用说明:
 *  1. 可通过 require 动态载入后在沙箱中执行(当前执行服务支持字符串形式)。
 *  2. 如需持久化版本控制，请将本文件纳入 git 并代码审查。
 *  3. 修改本文件后请注意不要引入非白名单模块。
 */

const executeLogic = async () => {
  try {
    const currentYear = 2024;
    const startYear = currentYear - 2;
    
    const pipeline = [
      {
        $match: {
          reportType: "按年度",
          fiscalYear: { $gte: startYear, $lte: currentYear }
        }
      },
      {
        $group: {
          _id: "$stockCode",
          roeData: {
            $push: {
              year: "$fiscalYear",
              roe: "$data.keyMetrics.returnOnEquity"
            }
          },
          count: { $sum: 1 }
        }
      },
      {
        $match: {
          count: 3
        }
      },
      {
        $addFields: {
          sortedRoeData: {
            $sortArray: {
              input: "$roeData",
              sortBy: { year: 1 }
            }
          }
        }
      },
      {
        $match: {
          $expr: {
            $and: [
              { $gt: [{ $arrayElemAt: ["$sortedRoeData.roe", 1] }, { $arrayElemAt: ["$sortedRoeData.roe", 0] }] },
              { $gt: [{ $arrayElemAt: ["$sortedRoeData.roe", 2] }, { $arrayElemAt: ["$sortedRoeData.roe", 1] }] },
              { $ne: [{ $arrayElemAt: ["$sortedRoeData.roe", 0] }, null] },
              { $ne: [{ $arrayElemAt: ["$sortedRoeData.roe", 1] }, null] },
              { $ne: [{ $arrayElemAt: ["$sortedRoeData.roe", 2] }, null] }
            ]
          }
        }
      },
      {
        $project: {
          stockCode: "$_id",
          roeTrend: "$sortedRoeData",
          _id: 0
        }
      }
    ];

    const result = await FinancialData.aggregate(pipeline);
    return result;
  } catch (error) {
    throw new Error(`查询失败: ${error.message}`);
  }
};
