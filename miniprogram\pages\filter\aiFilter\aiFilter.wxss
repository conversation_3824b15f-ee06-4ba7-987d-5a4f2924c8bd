/* 智能筛选页面样式 */
@import '../../../styles/base.wxss';

.ai-filter-page {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  padding-bottom: var(--spacing-xxl);
}

/* ==================== 页面标题 ==================== */
.page-header {
  background: var(--gradient-primary);
  padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-xxl);
  color: var(--text-inverse);
}

.title-section {
  text-align: center;
}

.page-title {
  font-size: var(--font-size-title);
  font-weight: var(--font-weight-bold);
  display: block;
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: var(--font-size-base);
  opacity: 0.9;
  line-height: 1.6;
  display: block;
}

/* ==================== 智能输入区域 ==================== */
.ai-input-section {
  margin: calc(-1 * var(--spacing-lg)) var(--spacing-lg) var(--spacing-xl);
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-card);
  overflow: hidden;
}

.input-container {
  padding: var(--spacing-xl);
}

.input-header {
  margin-bottom: var(--spacing-lg);
}

.input-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--spacing-xs);
}

.input-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.5;
  display: block;
}

.input-area {
  position: relative;
}

.ai-input {
  width: 100%;
  min-height: 200rpx;
  padding: var(--spacing-lg);
  border: 2rpx solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  line-height: 1.6;
  transition: var(--transition-base);
  box-sizing: border-box;
}

.ai-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4rpx rgba(27, 79, 114, 0.1);
}

.input-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--spacing-sm);
}

.char-count {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

/* ==================== 示例区域 ==================== */
.examples-section {
  padding: 0 var(--spacing-xl) var(--spacing-xl);
  border-top: 1rpx solid var(--divider);
  background-color: var(--bg-secondary);
}

.examples-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  display: block;
  margin-bottom: var(--spacing-base);
  margin-top: var(--spacing-lg);
}

.examples-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.example-item {
  padding: var(--spacing-base) var(--spacing-lg);
  background-color: var(--bg-primary);
  border-radius: var(--radius-base);
  border: 1rpx solid var(--border-light);
  transition: var(--transition-fast);
  cursor: pointer;
}

.example-item:active {
  background-color: var(--bg-tertiary);
  transform: scale(0.98);
}

.example-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.5;
}

/* ==================== 生成按钮 ==================== */
.generate-section {
  padding: 0 var(--spacing-lg) var(--spacing-xl);
}

.ai-filter-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--spacing-base);
}

.btn-parse {
  background: #1B4F72;
  color: #FFFFFF;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-parse.active {
  background: #1B4F72;
  color: #FFFFFF;
}

.btn-parse.active:active {
  opacity: 0.8;
}

.btn-parse.loading {
  background: #E0E0E0;
  color: #999999;
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-parse.disabled {
  background: #E0E0E0;
  color: #999999;
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* ==================== 渐进式生成区域 ==================== */
.progressive-section {
  margin: var(--spacing-lg);
  padding: var(--spacing-lg);
  background-color: var(--bg-card);
  border-radius: var(--radius-md);
}

/* 当前状态 */
.current-status {
  text-align: center;
  padding: var(--spacing-lg) 0;
  border-bottom: 1px solid var(--border-light);
  margin-bottom: var(--spacing-lg);
}

.status-text {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

/* 通用标题样式 */
.section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-light);
}

/* 逻辑分析结果 */
.logic-result {
  margin-bottom: var(--spacing-lg);
}

.logic-reason {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  line-height: 1.6;
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-sm);
}

/* 脚本生成进度 */
.scripts-progress {
  margin-bottom: var(--spacing-lg);
}

.progress-list {
  space: var(--spacing-sm);
}

.progress-item {
  padding: var(--spacing-md);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-sm);
}

.progress-item:last-child {
  margin-bottom: 0;
}

.item-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-sm);
}

.item-desc {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  line-height: 1.5;
  flex: 1;
  margin-right: var(--spacing-md);
}

.item-status {
  font-size: var(--font-size-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-xs);
  white-space: nowrap;
}

.item-status.pending {
  background-color: #f8f9fa;
  color: #6c757d;
}

.item-status.generating {
  background-color: #fff3cd;
  color: #856404;
}

.item-status.completed {
  background-color: #d4edda;
  color: #155724;
}

.item-status.failed {
  background-color: #f8d7da;
  color: #721c24;
}

.item-action {
  text-align: right;
}

.execute-btn {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.execute-btn:active {
  opacity: 0.8;
}

/* ==================== 结果展示区域 ==================== */
.result-section {
  margin: var(--spacing-lg);
  padding: var(--spacing-lg);
  background-color: var(--bg-card);
  border-radius: var(--radius-md);
}

/* 最终脚本列表 */
.final-scripts {
  margin-bottom: var(--spacing-lg);
}

.scripts-count {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.scripts-list {
  space: var(--spacing-sm);
}

.script-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-sm);
}

.script-item:last-child {
  margin-bottom: 0;
}

.script-info {
  flex: 1;
  margin-right: var(--spacing-md);
}

.script-desc {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  line-height: 1.5;
  margin-bottom: var(--spacing-xs);
}

.script-file {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 重新生成按钮 */
.regenerate-action {
  text-align: center;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
}

.regenerate-btn {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

.regenerate-btn:active {
  background-color: var(--border-light);
}



/* ==================== 错误状态 ==================== */
.error-section {
  margin: var(--spacing-lg);
  padding: var(--spacing-lg);
  background-color: var(--bg-card);
  border-radius: var(--radius-md);
}

.error-card {
  text-align: center;
}

.error-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-danger);
  margin-bottom: var(--spacing-sm);
}

.error-message {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
}

.btn-retry {
  background-color: var(--color-danger);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

.btn-retry:active {
  opacity: 0.8;
}



/* ==================== 响应式适配 ==================== */
@media screen and (max-width: 375px) {
  .ai-input-section {
    margin-left: var(--spacing-base);
    margin-right: var(--spacing-base);
  }

  .input-container {
    padding: var(--spacing-lg);
  }

  .generate-section {
    padding: 0 var(--spacing-base) var(--spacing-xl);
  }

  .progressive-section,
  .result-section,
  .error-section {
    margin: var(--spacing-base);
    padding: var(--spacing-base);
  }
}

/* ==================== 动画效果 ==================== */
.progressive-section,
.result-section,
.error-section {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==================== 深色模式支持 ==================== */
/* 注意：微信小程序对媒体查询支持有限，此部分可能需要通过JS动态控制 */
/*
@media (prefers-color-scheme: dark) {
  .ai-input {
    background-color: #2D2D2D;
    border-color: #404040;
    color: #FFFFFF;
  }

  .example-item {
    background-color: #2D2D2D;
    border-color: #404040;
  }

  .example-text {
    color: #B0B0B0;
  }
}
*/
