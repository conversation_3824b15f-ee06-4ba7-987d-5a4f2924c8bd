const axios = require('axios');

/**
 * AI筛选脚本生成功能 - HTTP测试
 * 适用于后端已启动的情况
 */

// API配置
const baseURL = 'http://localhost:3000'; // 请根据您的实际端口修改
const apiPrefix = '/api/v1/filter';
const endpoint = '/ai-generate-script';

/**
 * 测试函数
 */
async function testAiScriptGeneration() {
  console.log('🧪 开始测试AI筛选脚本生成API');
  console.log('📍 服务器地址:', baseURL);
  console.log('📅 测试时间:', new Date().toLocaleString('zh-CN'));
  console.log('='.repeat(60));

  // 定义测试用的自然语言查询
  const userQuery = '找出近三年净利润增长率平均超过20%，且ROE呈上升趋势的股票';

  console.log(`\n🗣️  发送自然语言查询: "${userQuery}"`);
  console.log('-'.repeat(40));

  try {
    const url = `${baseURL}${apiPrefix}${endpoint}`;
    const requestData = {
      userInput: userQuery,
    };

    console.log('📤 发送数据:', JSON.stringify(requestData, null, 2));

    const startTime = Date.now();
    const response = await axios.post(url, requestData);
    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log('📥 响应状态:', response.status);
    console.log('⏱️  响应时间:', `${duration}ms`);
    console.log('📥 响应数据:', JSON.stringify(response.data, null, 2));

    if (response.status === 200 && response.data.success) {
      console.log('✅ 测试通过');
      console.log('📄 生成的脚本内容:');
      console.log(response.data.data.scriptContent);
    } else {
      console.log('⚠️  测试未通过，响应异常');
    }

  } catch (error) {
    if (error.response) {
      console.log('📥 响应状态:', error.response.status);
      console.log('📥 错误响应:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('❌ 网络错误:', error.message);
      console.log('💡 请确保后端服务在', baseURL, '上运行');
    }
    console.log('❌ 测试失败');
  }

  console.log('\n🎉 测试完成！');
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  testAiScriptGeneration().catch(error => {
    console.error('💥 测试运行遭遇意外失败:', error.message);
  });
}

module.exports = { testAiScriptGeneration };
