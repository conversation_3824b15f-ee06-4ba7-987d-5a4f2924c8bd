/**
 * 测试渐进式生成功能
 * 运行方式: node test_progressive_generation.js
 */

const { 
  startProgressiveGeneration, 
  getTaskStatus,
  TASK_STATUS,
  SUB_LOGIC_STATUS
} = require('./src/services/filter/aiFilterScriptProgressiveService');

async function testProgressiveGeneration() {
  try {
    console.log('=== 测试渐进式AI筛选脚本生成 ===\n');

    // 测试启动任务
    console.log('1. 启动渐进式生成任务...');
    const userInput = '找出连续三年净资产收益率大于15%的股票';
    
    const startResult = await startProgressiveGeneration(userInput);
    console.log('启动结果:', startResult);
    
    if (!startResult.success) {
      throw new Error('启动任务失败');
    }

    const taskId = startResult.taskId;
    console.log(`任务ID: ${taskId}\n`);

    // 模拟轮询查询状态
    console.log('2. 开始轮询任务状态...');
    let pollCount = 0;
    const maxPolls = 30; // 最多轮询30次（60秒）

    while (pollCount < maxPolls) {
      pollCount++;
      
      try {
        const statusResult = await getTaskStatus(taskId);
        
        if (statusResult.success) {
          const taskData = statusResult.data;
          console.log(`[轮询 ${pollCount}] 状态: ${taskData.status}`);
          
          // 显示逻辑分析结果
          if (taskData.logicAnalysis && taskData.status === 'analyzed') {
            console.log('逻辑分析完成:', taskData.logicAnalysis);
          }
          
          // 显示子逻辑进度
          if (taskData.subLogics && taskData.subLogics.length > 0) {
            console.log('子逻辑进度:');
            taskData.subLogics.forEach(subLogic => {
              console.log(`  ${subLogic.index}. ${subLogic.description} - ${subLogic.status}`);
            });
          }
          
          // 检查是否完成
          if (taskData.status === 'completed') {
            console.log('\n✅ 任务完成！');
            console.log('最终结果:', JSON.stringify(taskData, null, 2));
            break;
          } else if (taskData.status === 'failed') {
            console.log('\n❌ 任务失败:', taskData.error);
            break;
          }
        }
      } catch (error) {
        console.log(`[轮询 ${pollCount}] 查询失败:`, error.message);
      }
      
      // 等待2秒后继续轮询
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    if (pollCount >= maxPolls) {
      console.log('\n⏰ 轮询超时，任务可能仍在进行中');
    }

  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  testProgressiveGeneration();
}

module.exports = { testProgressiveGeneration };
