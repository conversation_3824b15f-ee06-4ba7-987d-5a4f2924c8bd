# 渐进式AI筛选脚本生成功能

## 功能概述

本功能将原有的一次性AI筛选脚本生成改为渐进式生成，让用户能够实时看到生成进度：

1. **第一阶段**：大模型梳理逻辑，拆分子逻辑
2. **第二阶段**：逐个生成子逻辑的脚本，用户可以看到每个子逻辑的生成进度

## 技术实现

### 后端实现

#### 1. 新增服务文件
- `server/src/services/filter/aiFilterScriptProgressiveService.js`
  - 实现任务状态管理（内存存储，生产环境建议使用Redis）
  - 异步执行生成流程
  - 支持任务状态查询

#### 2. 新增控制器文件
- `server/src/controllers/filter/aiFilterScriptProgressiveController.js`
  - `startProgressiveGenerationController`: 启动渐进式生成任务
  - `getTaskStatusController`: 查询任务状态

#### 3. 新增验证器文件
- `server/src/validators/filter/aiFilterScriptProgressiveValidator.js`
  - 验证启动任务和查询状态的请求参数

#### 4. 新增路由
- `POST /api/filter/ai-generate-script-progressive`: 启动渐进式生成
- `GET /api/filter/ai-generate-script-progressive/:taskId`: 查询任务状态

### 前端实现

#### 1. API接口更新
- `miniprogram/api/filter.js`
  - `startProgressiveGeneration()`: 启动渐进式生成
  - `getProgressiveGenerationStatus()`: 查询任务状态

#### 2. 页面逻辑更新
- `miniprogram/pages/filter/aiFilter/aiFilter.js`
  - 添加渐进式生成相关数据字段
  - 实现轮询机制（每2秒查询一次状态）
  - 根据任务状态动态更新UI

#### 3. 页面模板更新
- `miniprogram/pages/filter/aiFilter/aiFilter.wxml`
  - 添加渐进式生成状态显示区域
  - 显示逻辑梳理结果
  - 显示子逻辑生成进度（带加载动画）

#### 4. 样式更新
- `miniprogram/pages/filter/aiFilter/aiFilter.wxss`
  - 添加渐进式生成相关样式
  - 加载动画样式
  - 状态指示器样式

## 任务状态说明

### 主任务状态
- `pending`: 等待开始
- `analyzing`: 正在分析逻辑
- `analyzed`: 逻辑分析完成
- `generating`: 正在生成脚本
- `completed`: 全部完成
- `failed`: 失败

### 子逻辑状态
- `pending`: 等待生成
- `generating`: 正在生成
- `completed`: 生成完成
- `failed`: 生成失败

## 用户体验流程

1. **用户点击"生成筛选脚本"**
   - 页面显示"大模型开始梳理逻辑"
   - 启动渐进式生成任务

2. **第一个Agent工作**
   - 状态显示"正在梳理逻辑"
   - 完成后显示"逻辑梳理完成"
   - 展示拆分后的子逻辑列表

3. **第二个Agent工作**
   - 状态显示"开始生成脚本"
   - 每个子逻辑显示生成进度：
     - 等待生成（灰色）
     - 生成中...（黄色+转圈动画）
     - 已完成（绿色）
     - 生成失败（红色）

4. **全部完成**
   - 显示"全部生成完成"
   - 用户可以执行生成的脚本

## 技术特点

### 1. 轮询机制
- 前端每2秒查询一次任务状态
- 页面隐藏/卸载时自动停止轮询
- 网络错误时继续重试，其他错误停止轮询

### 2. 状态管理
- 后端使用内存Map存储任务状态
- 生产环境建议改为Redis存储
- 支持任务超时和清理机制

### 3. 错误处理
- 完善的错误处理和用户提示
- 网络错误和业务错误分别处理
- 失败任务的状态保留和错误信息展示

### 4. 向后兼容
- 保留原有的一次性生成功能作为备用
- 通过`isProgressiveMode`标志控制使用哪种模式

## 部署说明

1. **安装依赖**
   ```bash
   cd server
   npm install uuid
   ```

2. **启动服务**
   - 确保后端服务正常运行
   - 新增的路由会自动生效

3. **测试功能**
   ```bash
   cd server
   node test_progressive_generation.js
   ```

## 注意事项

1. **内存使用**：当前使用内存存储任务状态，重启服务会丢失进行中的任务

2. **任务清理**：建议添加定时清理过期任务的机制

3. **并发限制**：建议添加并发任务数量限制

4. **错误恢复**：网络中断时前端会继续轮询，后端任务继续执行

5. **用户体验**：轮询频率可根据实际需要调整（当前2秒一次）

## 后续优化建议

1. **使用Redis**：将任务状态存储改为Redis，支持分布式部署
2. **WebSocket**：考虑使用WebSocket替代轮询，减少服务器压力
3. **任务队列**：使用专业的任务队列（如Bull）管理生成任务
4. **进度细化**：可以进一步细化每个子逻辑的生成进度
5. **任务恢复**：支持页面刷新后恢复任务状态查询
