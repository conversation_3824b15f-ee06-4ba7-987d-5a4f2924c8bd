/**
 * AI自动生成的筛选脚本 (勿直接在生产执行前未审核情况下使用)
 * 生成时间: 2025-09-25T06:48:52.281Z
 * 逻辑索引: 2
 * 逻辑原始描述: 分析近三年ROE的变化趋势，筛选出ROE呈上升趋势的股票
 *
 * 使用说明:
 *  1. 可通过 require 动态载入后在沙箱中执行(当前执行服务支持字符串形式)。
 *  2. 如需持久化版本控制，请将本文件纳入 git 并代码审查。
 *  3. 修改本文件后请注意不要引入非白名单模块。
 */

const executeLogic = async () => {
  try {
    const currentYear = 2024;
    const startYear = currentYear - 2;
    
    const result = await FinancialData.aggregate([
      {
        $match: {
          reportType: "按年度",
          fiscalYear: { $gte: startYear, $lte: currentYear }
        }
      },
      {
        $group: {
          _id: "$stockCode",
          roeData: {
            $push: {
              year: "$fiscalYear",
              roe: "$data.keyMetrics.returnOnEquity"
            }
          }
        }
      },
      {
        $project: {
          stockCode: "$_id",
          roeData: 1,
          hasCompleteData: {
            $eq: [{ $size: "$roeData" }, 3]
          },
          sortedRoeData: {
            $sortArray: {
              input: "$roeData",
              sortBy: { year: 1 }
            }
          }
        }
      },
      {
        $match: {
          hasCompleteData: true,
          "sortedRoeData.0.roe": { $ne: null, $gt: 0 },
          "sortedRoeData.1.roe": { $ne: null, $gt: 0 },
          "sortedRoeData.2.roe": { $ne: null, $gt: 0 }
        }
      },
      {
        $project: {
          stockCode: 1,
          roeTrend: {
            $and: [
              { $lt: ["$sortedRoeData.0.roe", "$sortedRoeData.1.roe"] },
              { $lt: ["$sortedRoeData.1.roe", "$sortedRoeData.2.roe"] }
            ]
          },
          roeData: "$sortedRoeData"
        }
      },
      {
        $match: {
          roeTrend: true
        }
      },
      {
        $project: {
          stockCode: 1,
          roeData: 1
        }
      }
    ]);
    
    return result;
  } catch (error) {
    throw new Error(`查询失败: ${error.message}`);
  }
};
